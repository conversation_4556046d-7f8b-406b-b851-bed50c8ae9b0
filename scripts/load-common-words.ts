#!/usr/bin/env tsx

import { PrismaClient, Language } from '@prisma/client';
import * as natural from 'natural';

const prisma = new PrismaClient();

// Common English words to load
const commonWords = [
	// Basic nouns
	'house', 'car', 'book', 'water', 'food', 'time', 'person', 'place', 'thing', 'way',
	'day', 'man', 'woman', 'child', 'life', 'world', 'school', 'work', 'home', 'family',
	
	// Basic verbs
	'run', 'walk', 'go', 'come', 'see', 'look', 'get', 'give', 'take', 'make',
	'know', 'think', 'say', 'tell', 'ask', 'work', 'play', 'help', 'move', 'live',
	
	// Basic adjectives
	'good', 'bad', 'big', 'small', 'new', 'old', 'long', 'short', 'high', 'low',
	'hot', 'cold', 'fast', 'slow', 'easy', 'hard', 'light', 'dark', 'clean', 'dirty',
	
	// Basic adverbs
	'well', 'quickly', 'slowly', 'carefully', 'easily', 'hardly', 'really', 'very',
	'quite', 'almost', 'always', 'never', 'sometimes', 'often', 'usually', 'here',
	'there', 'now', 'then', 'today'
];

interface WordNetEntry {
	term: string;
	synsets: string[];
	lemma: string | null;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
}

class CommonWordsLoader {
	private wordNet: any;

	constructor() {
		try {
			this.wordNet = new natural.WordNet();
			console.log('✅ WordNet initialized successfully');
		} catch (error) {
			console.error('❌ Failed to initialize WordNet:', error);
			throw error;
		}
	}

	/**
	 * Get WordNet information for a term
	 */
	private async getWordNetInfo(term: string): Promise<WordNetEntry | null> {
		return new Promise((resolve) => {
			try {
				this.wordNet.lookup(term, (results: any[]) => {
					if (!results || results.length === 0) {
						resolve(null);
						return;
					}

					const entry: WordNetEntry = {
						term: term.toLowerCase(),
						synsets: [],
						lemma: term.toLowerCase(),
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					};

					// Process each synset
					results.forEach((synset: any) => {
						// Add synset definition (gloss)
						if (synset.gloss) {
							entry.synsets.push(synset.gloss.trim());
						}

						// Add synonyms as hypernyms
						if (synset.synonyms && Array.isArray(synset.synonyms)) {
							synset.synonyms.forEach((synonym: string) => {
								if (synonym !== term && !entry.hypernyms.includes(synonym)) {
									entry.hypernyms.push(synonym);
								}
							});
						}
					});

					resolve(entry);
				});
			} catch (error) {
				console.error(`Error getting WordNet info for "${term}":`, error);
				resolve(null);
			}
		});
	}

	/**
	 * Load common words into database
	 */
	async loadCommonWords(): Promise<void> {
		console.log(`🚀 Loading ${commonWords.length} common words...`);
		
		let processedCount = 0;
		let savedCount = 0;
		let skippedCount = 0;

		for (const term of commonWords) {
			try {
				// Check if word already exists
				const existingWord = await prisma.word.findFirst({
					where: {
						term: term.toLowerCase(),
						language: Language.EN,
					},
					include: {
						wordnet_data: true,
					},
				});

				if (existingWord && existingWord.wordnet_data) {
					console.log(`⏭️  Skipping "${term}" - already exists with WordNet data`);
					skippedCount++;
					processedCount++;
					continue;
				}

				// Get WordNet info
				const wordNetInfo = await this.getWordNetInfo(term);
				
				if (!wordNetInfo) {
					console.log(`❌ No WordNet data found for "${term}"`);
					processedCount++;
					continue;
				}

				// Save to database
				await prisma.$transaction(async (tx) => {
					let wordId: string;

					if (existingWord) {
						wordId = existingWord.id;
					} else {
						// Create new word
						const newWord = await tx.word.create({
							data: {
								term: term.toLowerCase(),
								language: Language.EN,
							},
						});
						wordId = newWord.id;
					}

					// Create or update WordNet data
					await tx.wordNetData.upsert({
						where: { word_id: wordId },
						update: {
							synsets: wordNetInfo.synsets,
							lemma: wordNetInfo.lemma,
							hypernyms: wordNetInfo.hypernyms,
							hyponyms: wordNetInfo.hyponyms,
							holonyms: wordNetInfo.holonyms,
							meronyms: wordNetInfo.meronyms,
						},
						create: {
							word_id: wordId,
							synsets: wordNetInfo.synsets,
							lemma: wordNetInfo.lemma,
							hypernyms: wordNetInfo.hypernyms,
							hyponyms: wordNetInfo.hyponyms,
							holonyms: wordNetInfo.holonyms,
							meronyms: wordNetInfo.meronyms,
						},
					});
				});

				console.log(`✅ Saved "${term}" with ${wordNetInfo.synsets.length} synsets`);
				savedCount++;
				processedCount++;

				// Progress update
				const progress = Math.round((processedCount / commonWords.length) * 100);
				console.log(`📊 Progress: ${progress}% (${processedCount}/${commonWords.length})`);

			} catch (error) {
				console.error(`❌ Error processing "${term}":`, error);
				processedCount++;
			}
		}

		console.log('\n📈 Final Statistics:');
		console.log(`✅ Saved: ${savedCount} words`);
		console.log(`⏭️  Skipped: ${skippedCount} words`);
		console.log(`❌ Failed: ${processedCount - savedCount - skippedCount} words`);
		console.log(`📝 Total processed: ${processedCount} words`);
	}

	/**
	 * Cleanup resources
	 */
	async cleanup(): Promise<void> {
		await prisma.$disconnect();
	}
}

// Main function
async function main() {
	const loader = new CommonWordsLoader();

	try {
		await loader.loadCommonWords();
		console.log('\n🎉 Common words loading completed successfully!');
	} catch (error) {
		console.error('\n💥 Common words loading failed:', error);
		process.exit(1);
	} finally {
		await loader.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { CommonWordsLoader };
