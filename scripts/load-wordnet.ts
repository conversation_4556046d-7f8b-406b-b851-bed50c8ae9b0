#!/usr/bin/env tsx

import { PrismaClient, Language } from '@prisma/client';
import * as natural from 'natural';
import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';

const prisma = new PrismaClient();

interface WordNetEntry {
	term: string;
	pos: string;
	synsets: string[];
	lemma: string | null;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
}

interface LoadOptions {
	partOfSpeech?: string;
	batchSize?: number;
	maxWords?: number;
	resume?: boolean;
	dryRun?: boolean;
}

class WordNetLoader {
	private wordNet: any;
	private processedCount = 0;
	private errorCount = 0;
	private startTime = Date.now();

	constructor() {
		try {
			this.wordNet = new natural.WordNet();
			console.log('✅ WordNet initialized successfully');
		} catch (error) {
			console.error('❌ Failed to initialize WordNet:', error);
			throw error;
		}
	}

	/**
	 * Parse WordNet index file to get list of words
	 */
	private async parseIndexFile(filePath: string, maxWords?: number): Promise<string[]> {
		const words: string[] = [];
		const fileStream = fs.createReadStream(filePath);
		const rl = readline.createInterface({
			input: fileStream,
			crlfDelay: Infinity,
		});

		let lineCount = 0;
		for await (const line of rl) {
			lineCount++;
			
			// Skip header lines (first ~30 lines are license/header)
			if (lineCount <= 30) continue;
			
			// Skip empty lines
			if (!line.trim()) continue;

			// Parse word from line (first field)
			const parts = line.trim().split(/\s+/);
			if (parts.length > 0) {
				const word = parts[0].replace(/_/g, ' '); // Replace underscores with spaces
				words.push(word);
				
				if (maxWords && words.length >= maxWords) {
					break;
				}
			}
		}

		return words;
	}

	/**
	 * Get WordNet information for a term using natural library
	 */
	private async getWordNetInfo(term: string): Promise<WordNetEntry | null> {
		return new Promise((resolve) => {
			try {
				this.wordNet.lookup(term, (results: any[]) => {
					if (!results || results.length === 0) {
						resolve(null);
						return;
					}

					const entry: WordNetEntry = {
						term: term.toLowerCase(),
						pos: results[0].pos || 'n',
						synsets: [],
						lemma: term.toLowerCase(),
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					};

					// Process each synset
					results.forEach((synset: any) => {
						// Add synset definition (gloss)
						if (synset.gloss) {
							entry.synsets.push(synset.gloss.trim());
						}

						// Add synonyms as hypernyms for now
						if (synset.synonyms && Array.isArray(synset.synonyms)) {
							synset.synonyms.forEach((synonym: string) => {
								if (synonym !== term && !entry.hypernyms.includes(synonym)) {
									entry.hypernyms.push(synonym);
								}
							});
						}

						// Process pointers for relationships
						if (synset.ptrs && Array.isArray(synset.ptrs)) {
							synset.ptrs.forEach((ptr: any) => {
								// This is a simplified mapping - in a full implementation,
								// we would need to resolve pointer offsets to actual words
								switch (ptr.pointerSymbol) {
									case '@': // Hypernym (is-a)
										// Would need to resolve synsetOffset to actual word
										break;
									case '~': // Hyponym (reverse is-a)
										// Would need to resolve synsetOffset to actual word
										break;
									case '#p': // Part holonym
									case '#m': // Member holonym
										// Would need to resolve synsetOffset to actual word
										break;
									case '%p': // Part meronym
									case '%m': // Member meronym
										// Would need to resolve synsetOffset to actual word
										break;
								}
							});
						}
					});

					resolve(entry);
				});
			} catch (error) {
				console.error(`Error getting WordNet info for "${term}":`, error);
				resolve(null);
			}
		});
	}

	/**
	 * Load words from a specific part of speech
	 */
	async loadPartOfSpeech(pos: string, options: LoadOptions = {}): Promise<void> {
		const { batchSize = 100, maxWords, dryRun = false } = options;
		
		console.log(`\n🚀 Loading ${pos} words...`);
		
		// Get WordNet database path
		const wndb = require('wordnet-db');
		const indexFile = path.join(wndb.path, `index.${pos}`);
		
		if (!fs.existsSync(indexFile)) {
			console.error(`❌ Index file not found: ${indexFile}`);
			return;
		}

		// Parse words from index file
		console.log(`📖 Parsing ${indexFile}...`);
		const words = await this.parseIndexFile(indexFile, maxWords);
		console.log(`📝 Found ${words.length} words to process`);

		// Process words in batches
		for (let i = 0; i < words.length; i += batchSize) {
			const batch = words.slice(i, i + batchSize);
			await this.processBatch(batch, dryRun);
			
			// Progress update
			const progress = Math.round(((i + batch.length) / words.length) * 100);
			const elapsed = Math.round((Date.now() - this.startTime) / 1000);
			console.log(`📊 Progress: ${progress}% (${i + batch.length}/${words.length}) - ${elapsed}s elapsed`);
		}
	}

	/**
	 * Process a batch of words
	 */
	private async processBatch(words: string[], dryRun: boolean): Promise<void> {
		const entries: WordNetEntry[] = [];

		// Get WordNet info for each word
		for (const word of words) {
			try {
				const entry = await this.getWordNetInfo(word);
				if (entry) {
					entries.push(entry);
				}
				this.processedCount++;
			} catch (error) {
				console.error(`❌ Error processing word "${word}":`, error);
				this.errorCount++;
			}
		}

		if (dryRun) {
			console.log(`🔍 [DRY RUN] Would save ${entries.length} entries`);
			return;
		}

		// Save to database
		if (entries.length > 0) {
			await this.saveBatch(entries);
		}
	}

	/**
	 * Save batch of entries to database
	 */
	private async saveBatch(entries: WordNetEntry[]): Promise<void> {
		try {
			await prisma.$transaction(async (tx) => {
				for (const entry of entries) {
					// Check if word already exists
					const existingWord = await tx.word.findFirst({
						where: {
							term: entry.term,
							language: Language.EN,
						},
					});

					let wordId: string;

					if (existingWord) {
						wordId = existingWord.id;
					} else {
						// Create new word
						const newWord = await tx.word.create({
							data: {
								term: entry.term,
								language: Language.EN,
							},
						});
						wordId = newWord.id;
					}

					// Check if WordNet data already exists
					const existingWordNetData = await tx.wordNetData.findFirst({
						where: { word_id: wordId },
					});

					if (!existingWordNetData) {
						// Create WordNet data
						await tx.wordNetData.create({
							data: {
								word_id: wordId,
								synsets: entry.synsets,
								lemma: entry.lemma,
								hypernyms: entry.hypernyms,
								hyponyms: entry.hyponyms,
								holonyms: entry.holonyms,
								meronyms: entry.meronyms,
							},
						});
					}
				}
			});

			console.log(`✅ Saved ${entries.length} entries to database`);
		} catch (error) {
			console.error('❌ Error saving batch to database:', error);
			throw error;
		}
	}

	/**
	 * Load all parts of speech
	 */
	async loadAll(options: LoadOptions = {}): Promise<void> {
		const partsOfSpeech = ['noun', 'verb', 'adj', 'adv'];
		
		console.log('🎯 Starting WordNet data load...');
		console.log(`📋 Options:`, options);

		for (const pos of partsOfSpeech) {
			if (options.partOfSpeech && options.partOfSpeech !== pos) {
				continue;
			}
			
			await this.loadPartOfSpeech(pos, options);
		}

		// Final statistics
		const elapsed = Math.round((Date.now() - this.startTime) / 1000);
		console.log('\n📈 Final Statistics:');
		console.log(`✅ Processed: ${this.processedCount} words`);
		console.log(`❌ Errors: ${this.errorCount} words`);
		console.log(`⏱️  Total time: ${elapsed} seconds`);
	}

	/**
	 * Cleanup resources
	 */
	async cleanup(): Promise<void> {
		await prisma.$disconnect();
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	const options: LoadOptions = {};

	// Parse command line arguments
	for (let i = 0; i < args.length; i++) {
		switch (args[i]) {
			case '--pos':
				options.partOfSpeech = args[++i];
				break;
			case '--batch-size':
				options.batchSize = parseInt(args[++i]);
				break;
			case '--max-words':
				options.maxWords = parseInt(args[++i]);
				break;
			case '--dry-run':
				options.dryRun = true;
				break;
			case '--help':
				console.log(`
WordNet Data Loader

Usage: tsx scripts/load-wordnet.ts [options]

Options:
  --pos <pos>           Load specific part of speech (noun, verb, adj, adv)
  --batch-size <size>   Batch size for processing (default: 100)
  --max-words <count>   Maximum words to process (for testing)
  --dry-run            Run without saving to database
  --help               Show this help message

Examples:
  tsx scripts/load-wordnet.ts --pos noun --max-words 1000 --dry-run
  tsx scripts/load-wordnet.ts --batch-size 50
				`);
				process.exit(0);
		}
	}

	const loader = new WordNetLoader();

	try {
		await loader.loadAll(options);
		console.log('\n🎉 WordNet data load completed successfully!');
	} catch (error) {
		console.error('\n💥 WordNet data load failed:', error);
		process.exit(1);
	} finally {
		await loader.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { WordNetLoader };
