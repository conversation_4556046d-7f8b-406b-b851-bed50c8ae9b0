'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	Card<PERSON>ontent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Translate,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { Search, X, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { WordCard } from './components/word-card';
import { AllWordsSection } from './components/all-words-section';

export function VocabularyLookupClient() {
	const { t } = useTranslation();
	const { showError, showSuccess } = useToast();
	
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedLanguage, setSelectedLanguage] = useState<Language | 'ALL'>('ALL');
	const [searchResults, setSearchResults] = useState<WordDetail[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [hasSearched, setHasSearched] = useState(false);
	const [recentSearches, setRecentSearches] = useState<string[]>([]);

	// Load recent searches from localStorage
	useEffect(() => {
		const saved = localStorage.getItem('vocab-recent-searches');
		if (saved) {
			try {
				setRecentSearches(JSON.parse(saved));
			} catch (error) {
				console.error('Failed to load recent searches:', error);
			}
		}
	}, []);

	// Save recent searches to localStorage
	const saveRecentSearch = useCallback((term: string) => {
		const updated = [term, ...recentSearches.filter(s => s !== term)].slice(0, 10);
		setRecentSearches(updated);
		localStorage.setItem('vocab-recent-searches', JSON.stringify(updated));
	}, [recentSearches]);

	const handleSearch = useCallback(async () => {
		if (!searchTerm.trim()) {
			showError(t('vocabulary_lookup.search_error'));
			return;
		}

		setIsSearching(true);
		setHasSearched(true);

		try {
			const params = new URLSearchParams({
				term: searchTerm.trim(),
				limit: '20',
			});

			if (selectedLanguage !== 'ALL') {
				params.append('language', selectedLanguage);
			}

			const response = await fetch(`/api/words/search?${params}`);
			
			if (!response.ok) {
				throw new Error('Search failed');
			}

			const results = await response.json();
			setSearchResults(results);
			saveRecentSearch(searchTerm.trim());
			
			if (results.length === 0) {
				showSuccess(t('vocabulary_lookup.no_results'));
			}
		} catch (error) {
			console.error('Search error:', error);
			showError(t('vocabulary_lookup.search_error'));
			setSearchResults([]);
		} finally {
			setIsSearching(false);
		}
	}, [searchTerm, selectedLanguage, t, showError, showSuccess, saveRecentSearch]);

	const handleClear = useCallback(() => {
		setSearchTerm('');
		setSearchResults([]);
		setHasSearched(false);
	}, []);

	const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleSearch();
		}
	}, [handleSearch]);

	const clearRecentSearches = useCallback(() => {
		setRecentSearches([]);
		localStorage.removeItem('vocab-recent-searches');
	}, []);

	const handleRecentSearchClick = useCallback((term: string) => {
		setSearchTerm(term);
	}, []);

	return (
		<div className="container mx-auto px-4 py-8 max-w-6xl">
			{/* Header */}
			<div className="text-center mb-8">
				<h1 className="text-3xl font-bold mb-2">
					<Translate text="vocabulary_lookup.title" />
				</h1>
				<p className="text-muted-foreground">
					<Translate text="vocabulary_lookup.subtitle" />
				</p>
			</div>

			{/* Search Section */}
			<Card className="mb-8">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Search className="h-5 w-5" />
						<Translate text="vocabulary_lookup.search_button" />
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Search Input and Controls */}
					<div className="flex gap-4 items-end">
						<div className="flex-1">
							<Input
								type="text"
								placeholder={t('vocabulary_lookup.search_placeholder')}
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								onKeyPress={handleKeyPress}
								className="text-lg"
							/>
						</div>
						<div className="w-48">
							<Select
								value={selectedLanguage}
								onValueChange={(value) => setSelectedLanguage(value as Language | 'ALL')}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="ALL">
										<Translate text="vocabulary_lookup.all_languages" />
									</SelectItem>
									<SelectItem value="EN">English</SelectItem>
									<SelectItem value="VI">Tiếng Việt</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<Button
							onClick={handleSearch}
							disabled={isSearching || !searchTerm.trim()}
							className="px-6"
						>
							{isSearching ? (
								<>
									<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
									<Translate text="vocabulary_lookup.loading" />
								</>
							) : (
								<>
									<Search className="h-4 w-4 mr-2" />
									<Translate text="vocabulary_lookup.search_button" />
								</>
							)}
						</Button>
						{searchTerm && (
							<Button variant="outline" onClick={handleClear}>
								<X className="h-4 w-4 mr-2" />
								<Translate text="vocabulary_lookup.clear_button" />
							</Button>
						)}
					</div>

					{/* Recent Searches */}
					{recentSearches.length > 0 && (
						<div className="border-t pt-4">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-sm font-medium">
									<Translate text="vocabulary_lookup.recent_searches" />
								</h3>
								<Button
									variant="ghost"
									size="sm"
									onClick={clearRecentSearches}
									className="text-xs"
								>
									<Translate text="vocabulary_lookup.clear_recent" />
								</Button>
							</div>
							<div className="flex flex-wrap gap-2">
								{recentSearches.map((term, index) => (
									<Button
										key={index}
										variant="outline"
										size="sm"
										onClick={() => handleRecentSearchClick(term)}
										className="text-xs"
									>
										{term}
									</Button>
								))}
							</div>
						</div>
					)}

					{/* Search Hint */}
					<div className="text-sm text-muted-foreground">
						<Translate text="vocabulary_lookup.search_hint" />
					</div>
				</CardContent>
			</Card>

			{/* Search Results */}
			{hasSearched && (
				<Card className="mb-8">
					<CardHeader>
						<CardTitle>
							<Translate text="vocabulary_lookup.search_results" />
							{searchResults.length > 0 && (
								<span className="ml-2 text-sm font-normal text-muted-foreground">
									({searchResults.length} {searchResults.length === 1 ? 'word' : 'words'})
								</span>
							)}
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isSearching ? (
							<div className="flex items-center justify-center py-8">
								<LoadingSpinner className="h-8 w-8" />
								<span className="ml-2">
									<Translate text="vocabulary_lookup.loading" />
								</span>
							</div>
						) : searchResults.length > 0 ? (
							<div className="grid gap-4">
								{searchResults.map((word) => (
									<WordCard key={word.id} word={word} />
								))}
							</div>
						) : (
							<div className="text-center py-8 text-muted-foreground">
								<Translate text="vocabulary_lookup.no_results" />
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* All Words Section */}
			<AllWordsSection />
		</div>
	);
}
